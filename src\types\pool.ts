import type { InstrumentStatusEnum } from '@/enum';

/** 股票池详情 */
export interface PoolDetail {
  id: number;
  /** 证券代码 */
  instrument: string;
  /** 证券名称 */
  instrumentName: string;
  /** 状态 */
  status: InstrumentStatusEnum;
  /** 涨跌幅 */
  risePercent: number;
  /** 股票池名称 */
  poolName: string;
  /** 股票池ID */
  poolId: number;
  /** 仓位 */
  position: number;
}

/** 股票池 */
export interface Pool {
  id: number;
  /** 股票池名称 */
  name: string;
  /** 状态 */
  status: number;
  /** 类型 */
  type: number;
  /** 个股数量 */
  stockCount: number;
  /** 仓位 */
  position: number;
  /** 详情 */
  details: PoolDetail[];
}
