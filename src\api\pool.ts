import type { PoolDetail } from '@/types';
import http from './http';

class PoolService {
  /**
   * 获取监控池数据
   */
  static getMonitorPool() {
    return http<PoolDetail[]>('/api/poolinstruments', {
      method: 'GET',
    });
  }

  /**
   * 启动合约监控
   * @param id - 监控池合约ID
   */
  static startPoolInstrument(id: number) {
    return http<void>('/api/start_pool_instrument', {
      method: 'POST',
      data: {
        id,
        status,
      },
    });
  }

  /**
   * 停止合约监控
   * @param id - 监控池合约ID
   */
  static stopPoolInstrument(id: number) {
    return http<void>('/api/stop_pool_instrument', {
      method: 'POST',
      data: {
        id,
      },
    });
  }

  /**
   * 删除监控池合约
   * @param id - 监控池合约ID
   */
  static deleteMonitorPoolInstrument(id: number) {
    return http<void>('/api/deletepoolinstrument', {
      method: 'POST',
      data: {
        id,
      },
    });
  }
}

export default PoolService;
