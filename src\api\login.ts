import http from './http';
import type { SysUserInfo } from '@/types/user';

class LoginService {
  static login(username: string, password: string) {
    return http<SysUserInfo>('/api/login', {
      method: 'POST',
      data: {
        username,
        password,
      },
    });
  }

  // static logout() {
  //   return http<void>('/api/logout', {
  //     method: 'POST',
  //   });
  // }
}

export default LoginService;
