/**
 * 原始用户信息结构
 */
export interface PlainUserInfo {
  userId: number;
  userType: number;
  username: string;
  fullName: string;
  roleId: number;
  orgId: number;
  orgName: string;
  firstLogin: boolean;
  errorCode: number;
  token: string;
}

/**
 * 扩展后的用户信息结构
 */
export interface SysUserInfo extends PlainUserInfo {
  /** 用户角色名称 */
  roleName: string;
  /** 用户类型名称 */
  userTypeName: string;
  /** 登录密码 */
  password: string;

  /** 是否超级管理员 */
  isSuperAdmin: boolean;
  /** 是否券商运维人员 */
  isBrokerAdmin: boolean;
  /** 是否机构管理员 */
  isOrgAdmin: boolean;
  /** 是否产品经理 */
  isProductManager: boolean;
  /** 是否风控员 */
  isRiskProtector: boolean;
  /** 是否交易员 */
  isTradingMan: boolean;
  /** 是否投顾 */
  isCounselor: boolean;
  /** 是否观察员 */
  isObserver: boolean;
  /** 是否日内交易员 */
  isDayTrader: boolean;
  /** 该角色，是否需要登录行情服务器 */
  isQuoteServerRequired: boolean;
}
