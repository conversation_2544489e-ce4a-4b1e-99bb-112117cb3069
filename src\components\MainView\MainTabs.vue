<script setup lang="ts">
import { TABS } from '@/enum';
import { ref } from 'vue';

const activeName = ref<string>('AutoBuy');

defineExpose({
  activeName,
});
</script>

<template>
  <el-tabs type="card" v-model="activeName">
    <el-tab-pane
      v-for="tab in TABS"
      :key="tab.name"
      :label="tab.name"
      :name="tab.component"
    ></el-tab-pane>
  </el-tabs>
</template>

<style scoped></style>
